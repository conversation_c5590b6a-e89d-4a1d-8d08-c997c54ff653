<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Constellations du Zodiaque</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        
        #info {
            position: fixed;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 1000;
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
        }
        
        canvas {
            display: block;
        }
    </style>
</head>
<body>
    <div id="info">
        <h3>🌟 Constellations du Zodiaque - Test</h3>
        <p>✨ 12 constellations avec rotation planisphère</p>
        <p>🌌 1400+ étoiles de fond microscopiques</p>
        <p>💫 Effets d'éclat spectaculaires</p>
        <p>📝 Labels animés (apparition/disparition)</p>
        <p>🖱️ Interactions avec la souris</p>
    </div>
    
    <canvas id="constellation-canvas"></canvas>
    
    <script>
        // Configuration
        const canvas = document.getElementById('constellation-canvas');
        const ctx = canvas.getContext('2d');
        
        let animationId;
        let rotationAngle = 0;
        let mouseX = -1;
        let mouseY = -1;
        let labelVisibility = new Map();
        
        // Redimensionner le canvas
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        
        // Constellations du zodiaque (version simplifiée pour le test)
        const constellations = [
            {
                name: 'ARIES',
                label: { x: 0.125, y: 0.08, text: 'A R I E S' },
                stars: [
                    { x: 0.15, y: 0.25, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                    { x: 0.12, y: 0.18, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 1 },
                    { x: 0.08, y: 0.22, size: 1.5, brightness: 0.6, pulseSpeed: 0.002, pulsePhase: 2 }
                ],
                connections: [[0, 1], [1, 2]]
            },
            {
                name: 'TAURUS',
                label: { x: 0.375, y: 0.08, text: 'T A U R U S' },
                stars: [
                    { x: 0.35, y: 0.12, size: 2.8, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                    { x: 0.38, y: 0.18, size: 2.0, brightness: 0.9, pulseSpeed: 0.004, pulsePhase: 1 },
                    { x: 0.42, y: 0.15, size: 1.8, brightness: 0.7, pulseSpeed: 0.002, pulsePhase: 2 }
                ],
                connections: [[0, 1], [1, 2]]
            },
            {
                name: 'GEMINI',
                label: { x: 0.625, y: 0.08, text: 'G E M I N I' },
                stars: [
                    { x: 0.60, y: 0.12, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                    { x: 0.65, y: 0.12, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 1 },
                    { x: 0.62, y: 0.25, size: 1.5, brightness: 0.6, pulseSpeed: 0.002, pulsePhase: 4 }
                ],
                connections: [[0, 1], [0, 2], [1, 2]]
            }
        ];
        
        // Étoiles de fond
        const backgroundStars = [];
        for (let i = 0; i < 500; i++) {
            backgroundStars.push({
                x: Math.random(),
                y: Math.random(),
                size: Math.random() * 0.5 + 0.1,
                opacity: Math.random() * 0.3 + 0.1,
                pulseSpeed: Math.random() * 0.002 + 0.001,
                pulsePhase: Math.random() * Math.PI * 2
            });
        }
        
        // Fonction de rotation
        function applyRotation(x, y, centerX, centerY, angle) {
            const dx = x - centerX;
            const dy = y - centerY;
            const cos = Math.cos(angle);
            const sin = Math.sin(angle);
            
            return {
                x: dx * cos - dy * sin + centerX,
                y: dx * sin + dy * cos + centerY
            };
        }
        
        // Dessiner une étoile avec effets
        function drawStar(x, y, size, brightness, time, pulseSpeed, pulsePhase) {
            const twinkle = Math.sin(time * pulseSpeed + pulsePhase) * 0.3 + 0.7;
            const sparkle = Math.sin(time * pulseSpeed * 2.5 + pulsePhase) * 0.2 + 0.8;
            const opacity = brightness * twinkle;
            
            ctx.save();
            
            // Halo externe pour les étoiles brillantes
            if (brightness > 0.8) {
                ctx.globalAlpha = opacity * 0.1;
                const gradient = ctx.createRadialGradient(x, y, 0, x, y, size * 8);
                gradient.addColorStop(0, '#FFFFFF');
                gradient.addColorStop(0.2, '#FFFFFF40');
                gradient.addColorStop(1, '#FFFFFF00');
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(x, y, size * 8, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // Halo moyen
            if (brightness > 0.6) {
                ctx.globalAlpha = opacity * 0.3;
                const gradient = ctx.createRadialGradient(x, y, 0, x, y, size * 4);
                gradient.addColorStop(0, '#FFFFFF');
                gradient.addColorStop(0.4, '#FFFFFF80');
                gradient.addColorStop(1, '#FFFFFF00');
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(x, y, size * 4, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // Rayons pour les étoiles principales
            if (brightness > 0.9) {
                ctx.globalAlpha = opacity * sparkle * 0.6;
                ctx.strokeStyle = '#FFFFFF';
                ctx.lineWidth = 0.5;
                
                for (let i = 0; i < 4; i++) {
                    const angle = (i * Math.PI / 2) + (time * 0.001);
                    const rayLength = size * 6;
                    
                    ctx.beginPath();
                    ctx.moveTo(x + Math.cos(angle) * size, y + Math.sin(angle) * size);
                    ctx.lineTo(x + Math.cos(angle) * rayLength, y + Math.sin(angle) * rayLength);
                    ctx.stroke();
                }
            }
            
            // Corps de l'étoile
            ctx.globalAlpha = opacity;
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
        }
        
        // Animation principale
        function animate(time) {
            ctx.fillStyle = '#000008';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Rotation lente
            rotationAngle += 0.0002;
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // Étoiles de fond
            backgroundStars.forEach(star => {
                const twinkle = Math.sin(time * star.pulseSpeed + star.pulsePhase) * 0.4 + 0.6;
                const opacity = star.opacity * twinkle;
                
                ctx.save();
                ctx.globalAlpha = opacity * 1.5;
                ctx.fillStyle = '#FFFFFF';
                ctx.beginPath();
                ctx.arc(star.x * canvas.width, star.y * canvas.height, star.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            });
            
            // Constellations
            constellations.forEach(constellation => {
                // Dessiner les connexions
                ctx.save();
                ctx.globalAlpha = 0.4;
                ctx.strokeStyle = '#FFFFFF';
                ctx.lineWidth = 1;
                
                constellation.connections.forEach(([startIdx, endIdx]) => {
                    const startStar = constellation.stars[startIdx];
                    const endStar = constellation.stars[endIdx];
                    
                    if (startStar && endStar) {
                        const startPos = applyRotation(
                            startStar.x * canvas.width, 
                            startStar.y * canvas.height, 
                            centerX, centerY, rotationAngle
                        );
                        const endPos = applyRotation(
                            endStar.x * canvas.width, 
                            endStar.y * canvas.height, 
                            centerX, centerY, rotationAngle
                        );
                        
                        ctx.beginPath();
                        ctx.moveTo(startPos.x, startPos.y);
                        ctx.lineTo(endPos.x, endPos.y);
                        ctx.stroke();
                    }
                });
                ctx.restore();
                
                // Dessiner les étoiles
                constellation.stars.forEach(star => {
                    const pos = applyRotation(
                        star.x * canvas.width, 
                        star.y * canvas.height, 
                        centerX, centerY, rotationAngle
                    );
                    
                    drawStar(pos.x, pos.y, star.size, star.brightness, time, star.pulseSpeed, star.pulsePhase);
                });
                
                // Label animé
                let labelState = labelVisibility.get(constellation.name);
                if (!labelState) {
                    labelState = {
                        visible: false,
                        fadePhase: 0,
                        nextToggle: time + Math.random() * 5000 + 3000
                    };
                    labelVisibility.set(constellation.name, labelState);
                }
                
                if (time > labelState.nextToggle) {
                    labelState.visible = !labelState.visible;
                    labelState.fadePhase = 0;
                    labelState.nextToggle = time + (labelState.visible ? 
                        Math.random() * 3000 + 4000 : 
                        Math.random() * 4000 + 2000
                    );
                }
                
                if (labelState.visible) {
                    labelState.fadePhase = Math.min(1, labelState.fadePhase + 0.02);
                } else {
                    labelState.fadePhase = Math.max(0, labelState.fadePhase - 0.02);
                }
                
                if (labelState.fadePhase > 0) {
                    const labelPos = applyRotation(
                        constellation.label.x * canvas.width, 
                        constellation.label.y * canvas.height, 
                        centerX, centerY, rotationAngle
                    );
                    
                    const opacity = labelState.fadePhase * 0.8;
                    
                    ctx.save();
                    ctx.globalAlpha = opacity;
                    ctx.fillStyle = '#FFFFFF';
                    ctx.font = 'bold 14px Arial';
                    ctx.textAlign = 'center';
                    ctx.shadowColor = '#FFFFFF';
                    ctx.shadowBlur = 10;
                    ctx.fillText(constellation.label.text, labelPos.x, labelPos.y);
                    ctx.restore();
                }
            });
            
            animationId = requestAnimationFrame(animate);
        }
        
        // Gestionnaires d'événements
        canvas.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
        });
        
        canvas.addEventListener('mouseleave', () => {
            mouseX = -1;
            mouseY = -1;
        });
        
        window.addEventListener('resize', resizeCanvas);
        
        // Initialisation
        resizeCanvas();
        animate(0);
    </script>
</body>
</html>
