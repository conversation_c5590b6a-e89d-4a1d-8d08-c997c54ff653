import React, { useRef, useEffect, useMemo } from 'react';

interface Star {
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    opacity: number;
    pulseSpeed: number;
    pulsePhase: number;
    colorIndex: number;
    depth: number;
}

interface NebulaCloud {
    x: number;
    y: number;
    size: number;
    colorIndex: number;
    opacity: number;
    driftX: number;
    driftY: number;
    pulseSpeed: number;
    pulsePhase: number;
}

const ConstellationBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const animationIdRef = useRef<number>();
    const lastFrameTimeRef = useRef<number>(0);
    const fpsLimitRef = useRef<number>(60);

    // Cache des gradients pour éviter les recréations
    const gradientCacheRef = useRef<Map<string, CanvasGradient>>(new Map());

    // Configuration optimisée
    const config = useMemo(() => ({
        maxDistance: 120,
        starCount: 300,
        mouseInfluence: 100,
        mouseConnections: true,
        nebulaCloudCount: 4,
        nebulaOpacity: 0.06,
        nebulaMovementSpeed: 0.00005,
        nebulaEnabled: true,
        celestialRotationSpeed: 0.000003,
        rotationSmoothness: 0.98,
    }), []);

    // Couleurs optimisées
    const starColors = useMemo(() => [
        '#FFFFFF', '#F8F8FF', '#87CEEB', '#FFE4B5', '#FF6347'
    ], []);

    const nebulaColors = useMemo(() => [
        '#4B0082', '#8A2BE2', '#FF69B4', '#00CED1'
    ], []);

    // Cache trigonométrique
    const trigCacheRef = useRef<Map<string, { cos: number; sin: number }>>(new Map());

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d', {
            alpha: false,
            desynchronized: true
        });
        if (!ctx) return;

        let stars: Star[] = [];
        let nebulaClouds: NebulaCloud[] = [];
        let mouseX = 0;
        let mouseY = 0;
        let centerX = 0;
        let centerY = 0;

        const initialize = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            centerX = canvas.width * 0.6;
            centerY = canvas.height * 0.3;

            stars = [];
            nebulaClouds = [];
            gradientCacheRef.current.clear();

            // Génération des nuages de nébuleuse
            for (let i = 0; i < config.nebulaCloudCount; i++) {
                nebulaClouds.push({
                    x: Math.random() * canvas.width * 1.2 - canvas.width * 0.1,
                    y: Math.random() * canvas.height * 1.2 - canvas.height * 0.1,
                    size: Math.random() * 300 + 150,
                    colorIndex: Math.floor(Math.random() * nebulaColors.length),
                    opacity: Math.random() * config.nebulaOpacity + config.nebulaOpacity * 0.5,
                    driftX: (Math.random() - 0.5) * config.nebulaMovementSpeed,
                    driftY: (Math.random() - 0.5) * config.nebulaMovementSpeed,
                    pulseSpeed: Math.random() * 0.0008 + 0.0003,
                    pulsePhase: Math.random() * Math.PI * 2
                });
            }

            // Génération des étoiles optimisée
            for (let i = 0; i < config.starCount; i++) {
                const x = Math.random() * canvas.width;
                const y = Math.random() * canvas.height;

                // 2 couches seulement
                const depthLayer = Math.random();
                let size, opacity, pulseSpeed, colorIndex, depth;

                if (depthLayer < 0.6) {
                    // Arrière-plan (60%)
                    size = Math.random() * 1.2 + 0.3;
                    opacity = Math.random() * 0.4 + 0.2;
                    pulseSpeed = Math.random() * 0.003 + 0.001;
                    colorIndex = Math.floor(Math.random() * 3);
                    depth = 0.5;
                } else {
                    // Avant-plan (40%)
                    size = Math.random() * 2.0 + 1.0;
                    opacity = Math.random() * 0.6 + 0.4;
                    pulseSpeed = Math.random() * 0.005 + 0.002;
                    colorIndex = Math.floor(Math.random() * starColors.length);
                    depth = 1.0;
                }

                stars.push({
                    x, y,
                    vx: (Math.random() - 0.5) * 0.04 * depth,
                    vy: (Math.random() - 0.5) * 0.04 * depth,
                    size, opacity, pulseSpeed,
                    pulsePhase: Math.random() * Math.PI * 2,
                    colorIndex, depth
                });
            }
        };

        // Rotation céleste optimisée
        const applyCelestialRotation = (star: Star, time: number) => {
            const dx = star.x - centerX;
            const dy = star.y - centerY;

            const rotationAngle = time * config.celestialRotationSpeed * config.rotationSmoothness;

            // Cache trigonométrique
            const cacheKey = Math.floor(rotationAngle * 1000).toString();
            let trigValues = trigCacheRef.current.get(cacheKey);

            if (!trigValues) {
                trigValues = {
                    cos: Math.cos(rotationAngle),
                    sin: Math.sin(rotationAngle)
                };
                if (trigCacheRef.current.size > 100) {
                    trigCacheRef.current.clear();
                }
                trigCacheRef.current.set(cacheKey, trigValues);
            }

            const rotatedX = dx * trigValues.cos - dy * trigValues.sin;
            const rotatedY = dx * trigValues.sin + dy * trigValues.cos;

            return {
                x: rotatedX + centerX,
                y: rotatedY + centerY
            };
        };

        // Rendu nébuleuse optimisé
        const drawNebulaCloud = (cloud: NebulaCloud, time: number) => {
            if (!config.nebulaEnabled) return;

            const pulse = Math.sin(time * cloud.pulseSpeed + cloud.pulsePhase) * 0.2 + 0.8;
            const currentOpacity = cloud.opacity * pulse;
            const color = nebulaColors[cloud.colorIndex];

            // Cache des gradients
            const gradientKey = `nebula_${cloud.colorIndex}_${Math.floor(cloud.size)}`;
            let gradient = gradientCacheRef.current.get(gradientKey);

            if (!gradient) {
                gradient = ctx.createRadialGradient(
                    cloud.x, cloud.y, 0,
                    cloud.x, cloud.y, cloud.size
                );
                gradient.addColorStop(0, color + '00');
                gradient.addColorStop(0.3, color + '30');
                gradient.addColorStop(0.7, color + '20');
                gradient.addColorStop(1, color + '00');
                gradientCacheRef.current.set(gradientKey, gradient);
            }

            ctx.save();
            ctx.globalAlpha = currentOpacity;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(cloud.x, cloud.y, cloud.size, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        };

        // Rendu d'étoile ultra-optimisé
        const drawStar = (star: Star, time: number) => {
            const rotatedPos = applyCelestialRotation(star, time);

            // Scintillement simplifié
            const twinkle = Math.sin(time * star.pulseSpeed + star.pulsePhase) * 0.15 + 0.85;
            const currentOpacity = star.opacity * twinkle * star.depth;
            const color = starColors[star.colorIndex] || starColors[0];

            ctx.save();
            ctx.globalAlpha = currentOpacity;

            // Halo seulement pour les grandes étoiles
            if (star.size > 1.0) {
                const gradientKey = `star_${star.colorIndex}_${Math.floor(star.size * 10)}`;
                let gradient = gradientCacheRef.current.get(gradientKey);

                if (!gradient) {
                    gradient = ctx.createRadialGradient(
                        rotatedPos.x, rotatedPos.y, 0,
                        rotatedPos.x, rotatedPos.y, star.size * 2
                    );
                    gradient.addColorStop(0, color);
                    gradient.addColorStop(0.5, color + '80');
                    gradient.addColorStop(1, color + '00');
                    gradientCacheRef.current.set(gradientKey, gradient);
                }

                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(rotatedPos.x, rotatedPos.y, star.size * 2, 0, Math.PI * 2);
                ctx.fill();
            }

            // Corps de l'étoile
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(rotatedPos.x, rotatedPos.y, star.size, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();
            return rotatedPos;
        };

        // Connexions optimisées
        const drawConnection = (star1: Star, pos1: {x: number, y: number}, pos2: {x: number, y: number}, distance: number) => {
            const opacity = Math.max(0, (config.maxDistance - distance) / config.maxDistance) * 0.05;
            if (opacity < 0.01) return;

            const color1 = starColors[star1.colorIndex] || starColors[0];

            ctx.save();
            ctx.globalAlpha = opacity;
            ctx.strokeStyle = color1;
            ctx.lineWidth = 0.5;
            ctx.beginPath();
            ctx.moveTo(pos1.x, pos1.y);
            ctx.lineTo(pos2.x, pos2.y);
            ctx.stroke();
            ctx.restore();
        };

        // Connexions souris optimisées
        const drawMouseConnections = (time: number) => {
            if (mouseX < 0 || mouseY < 0 || !config.mouseConnections) return;

            // Limiter à 10 connexions max
            const nearbyStars = stars.filter(star => {
                const rotatedPos = applyCelestialRotation(star, time);
                const dx = mouseX - rotatedPos.x;
                const dy = mouseY - rotatedPos.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                return distance < config.mouseInfluence;
            }).slice(0, 10);

            nearbyStars.forEach(star => {
                const rotatedPos = applyCelestialRotation(star, time);
                const dx = mouseX - rotatedPos.x;
                const dy = mouseY - rotatedPos.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                const opacity = Math.max(0, (config.mouseInfluence - distance) / config.mouseInfluence) * 0.4;

                ctx.save();
                ctx.globalAlpha = opacity;
                ctx.strokeStyle = starColors[star.colorIndex] || starColors[0];
                ctx.lineWidth = 1.5;
                ctx.beginPath();
                ctx.moveTo(rotatedPos.x, rotatedPos.y);
                ctx.lineTo(mouseX, mouseY);
                ctx.stroke();
                ctx.restore();
            });

            // Point souris
            if (nearbyStars.length > 0) {
                ctx.save();
                ctx.globalAlpha = 0.3;
                ctx.fillStyle = '#FFFFFF';
                ctx.beginPath();
                ctx.arc(mouseX, mouseY, 8, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        };

        // Animation ultra-optimisée avec limitation FPS
        const animate = (time: number) => {
            const deltaTime = time - lastFrameTimeRef.current;
            const targetFrameTime = 1000 / fpsLimitRef.current;

            if (deltaTime < targetFrameTime) {
                animationIdRef.current = requestAnimationFrame(animate);
                return;
            }

            lastFrameTimeRef.current = time;

            // Fond noir
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Nébuleuse
            if (config.nebulaEnabled) {
                nebulaClouds.forEach(cloud => {
                    cloud.x += cloud.driftX;
                    cloud.y += cloud.driftY;

                    // Rebouclage
                    if (cloud.x < -cloud.size) cloud.x = canvas.width + cloud.size;
                    if (cloud.x > canvas.width + cloud.size) cloud.x = -cloud.size;
                    if (cloud.y < -cloud.size) cloud.y = canvas.height + cloud.size;
                    if (cloud.y > canvas.height + cloud.size) cloud.y = -cloud.size;

                    drawNebulaCloud(cloud, time);
                });
            }

            // Mise à jour des étoiles
            stars.forEach(star => {
                if (mouseX >= 0 && mouseY >= 0) {
                    const rotatedPos = applyCelestialRotation(star, time);
                    const dx = mouseX - rotatedPos.x;
                    const dy = mouseY - rotatedPos.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < config.mouseInfluence && distance > 0) {
                        const force = (config.mouseInfluence - distance) / config.mouseInfluence;
                        star.vx += (dx / distance) * force * 0.003;
                        star.vy += (dy / distance) * force * 0.003;
                    }
                }

                star.x += star.vx;
                star.y += star.vy;
                star.vx *= 0.98;
                star.vy *= 0.98;

                // Rebonds
                if (star.x < 0 || star.x > canvas.width) star.vx *= -1;
                if (star.y < 0 || star.y > canvas.height) star.vy *= -1;
                star.x = Math.max(0, Math.min(canvas.width, star.x));
                star.y = Math.max(0, Math.min(canvas.height, star.y));
            });

            // Positions rotées
            const rotatedPositions = stars.map(star => applyCelestialRotation(star, time));

            // Connexions optimisées - 1 étoile sur 3
            const connectionStep = 3;
            for (let i = 0; i < stars.length; i += connectionStep) {
                for (let j = i + connectionStep; j < stars.length; j += connectionStep) {
                    const pos1 = rotatedPositions[i];
                    const pos2 = rotatedPositions[j];
                    const dx = pos1.x - pos2.x;
                    const dy = pos1.y - pos2.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < config.maxDistance) {
                        drawConnection(stars[i], pos1, pos2, distance);
                    }
                }
            }

            // Connexions souris
            drawMouseConnections(time);

            // Rendu des étoiles
            stars.forEach(star => drawStar(star, time));

            animationIdRef.current = requestAnimationFrame(animate);
        };

        // Gestionnaires d'événements optimisés
        const handleResize = () => {
            initialize();
        };

        const handleMouseMove = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        const handleMouseLeave = () => {
            mouseX = -1;
            mouseY = -1;
        };

        const handleMouseEnter = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        // Initialisation et démarrage
        initialize();
        animate(0);

        // Event listeners
        window.addEventListener('resize', handleResize);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.addEventListener('mouseleave', handleMouseLeave);
        canvas.addEventListener('mouseenter', handleMouseEnter);

        return () => {
            if (animationIdRef.current) {
                cancelAnimationFrame(animationIdRef.current);
            }
            window.removeEventListener('resize', handleResize);
            canvas.removeEventListener('mousemove', handleMouseMove);
            canvas.removeEventListener('mouseleave', handleMouseLeave);
            canvas.removeEventListener('mouseenter', handleMouseEnter);
        };
    }, []);

    return (
        <div className="fixed top-0 left-0 w-full h-full -z-10" style={{ background: '#000' }}>
            <canvas
                ref={canvasRef}
                className="absolute top-0 left-0 w-full h-full"
                style={{
                    pointerEvents: 'auto',
                    zIndex: 1
                }}
            />
        </div>
    );
};

export default ConstellationBackground;
