import React, { useRef, useEffect, useMemo } from 'react';

interface ConstellationStar {
    x: number;
    y: number;
    size: number;
    brightness: number; // 0.3 à 1.0 pour différentes luminosités
    pulseSpeed: number;
    pulsePhase: number;
}

interface Constellation {
    name: string;
    stars: ConstellationStar[];
    connections: [number, number][]; // indices des étoiles à connecter
    position: { x: number; y: number }; // position relative de la constellation
    label: { x: number; y: number; text: string };
}

interface BackgroundStar {
    x: number;
    y: number;
    size: number;
    opacity: number;
    pulseSpeed: number;
    pulsePhase: number;
}

const ConstellationBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const animationIdRef = useRef<number>();
    const lastFrameTimeRef = useRef<number>(0);

    // Définition des 12 constellations du zodiaque
    const zodiacConstellations = useMemo((): Constellation[] => [
        // ARIES (Bélier) - Ligne 1, Position 1
        {
            name: 'ARIES',
            position: { x: 0.125, y: 0.15 },
            label: { x: 0.125, y: 0.08, text: 'A R I E S' },
            stars: [
                { x: 0.15, y: 0.25, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.12, y: 0.18, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.08, y: 0.22, size: 1.5, brightness: 0.6, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.18, y: 0.15, size: 1.2, brightness: 0.5, pulseSpeed: 0.005, pulsePhase: 3 }
            ],
            connections: [[0, 1], [1, 2], [1, 3]]
        },
        // TAURUS (Taureau) - Ligne 1, Position 2
        {
            name: 'TAURUS',
            position: { x: 0.375, y: 0.15 },
            label: { x: 0.375, y: 0.08, text: 'T A U R U S' },
            stars: [
                { x: 0.35, y: 0.12, size: 2.8, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.38, y: 0.18, size: 2.0, brightness: 0.9, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.42, y: 0.15, size: 1.8, brightness: 0.7, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.32, y: 0.22, size: 1.5, brightness: 0.6, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.40, y: 0.25, size: 1.3, brightness: 0.5, pulseSpeed: 0.003, pulsePhase: 4 },
                { x: 0.36, y: 0.28, size: 1.2, brightness: 0.4, pulseSpeed: 0.004, pulsePhase: 5 }
            ],
            connections: [[0, 1], [1, 2], [0, 3], [3, 4], [4, 5], [1, 4]]
        },
        // GEMINI (Gémeaux) - Ligne 1, Position 3
        {
            name: 'GEMINI',
            position: { x: 0.625, y: 0.15 },
            label: { x: 0.625, y: 0.08, text: 'G E M I N I' },
            stars: [
                { x: 0.60, y: 0.12, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.65, y: 0.12, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 1 },
                { x: 0.58, y: 0.18, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 2 },
                { x: 0.67, y: 0.18, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 3 },
                { x: 0.62, y: 0.25, size: 1.5, brightness: 0.6, pulseSpeed: 0.002, pulsePhase: 4 },
                { x: 0.55, y: 0.22, size: 1.3, brightness: 0.5, pulseSpeed: 0.005, pulsePhase: 5 },
                { x: 0.70, y: 0.22, size: 1.3, brightness: 0.5, pulseSpeed: 0.005, pulsePhase: 6 }
            ],
            connections: [[0, 2], [2, 5], [5, 4], [4, 6], [6, 3], [3, 1], [0, 1]]
        }
    ], []);

    // Toutes les constellations du zodiaque (complètes)
    const allConstellations = useMemo((): Constellation[] => [
        ...zodiacConstellations,
        // CANCER (Cancer) - Ligne 1, Position 4
        {
            name: 'CANCER',
            position: { x: 0.875, y: 0.15 },
            label: { x: 0.875, y: 0.08, text: 'C A N C E R' },
            stars: [
                { x: 0.85, y: 0.12, size: 2.0, brightness: 0.9, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.88, y: 0.18, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.90, y: 0.25, size: 2.2, brightness: 1.0, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.87, y: 0.28, size: 1.5, brightness: 0.6, pulseSpeed: 0.005, pulsePhase: 3 }
            ],
            connections: [[0, 1], [1, 2], [2, 3]]
        },
        // LEO (Lion) - Ligne 2, Position 1
        {
            name: 'LEO',
            position: { x: 0.125, y: 0.4 },
            label: { x: 0.125, y: 0.33, text: 'L E O' },
            stars: [
                { x: 0.08, y: 0.38, size: 2.8, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.12, y: 0.42, size: 2.0, brightness: 0.9, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.16, y: 0.45, size: 1.8, brightness: 0.8, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.14, y: 0.38, size: 1.5, brightness: 0.7, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.18, y: 0.40, size: 1.3, brightness: 0.6, pulseSpeed: 0.003, pulsePhase: 4 },
                { x: 0.10, y: 0.48, size: 1.2, brightness: 0.5, pulseSpeed: 0.004, pulsePhase: 5 }
            ],
            connections: [[0, 1], [1, 3], [3, 4], [4, 2], [1, 5]]
        },
        // VIRGO (Vierge) - Ligne 2, Position 2
        {
            name: 'VIRGO',
            position: { x: 0.375, y: 0.4 },
            label: { x: 0.375, y: 0.33, text: 'V I R G O' },
            stars: [
                { x: 0.35, y: 0.38, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.38, y: 0.42, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.42, y: 0.45, size: 1.6, brightness: 0.7, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.40, y: 0.38, size: 1.4, brightness: 0.6, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.36, y: 0.48, size: 1.3, brightness: 0.5, pulseSpeed: 0.003, pulsePhase: 4 }
            ],
            connections: [[0, 1], [1, 3], [3, 2], [1, 4]]
        },
        // LIBRA (Balance) - Ligne 2, Position 3
        {
            name: 'LIBRA',
            position: { x: 0.625, y: 0.4 },
            label: { x: 0.625, y: 0.33, text: 'L I B R A' },
            stars: [
                { x: 0.60, y: 0.38, size: 2.0, brightness: 0.9, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.65, y: 0.42, size: 2.2, brightness: 1.0, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.62, y: 0.48, size: 1.8, brightness: 0.8, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.58, y: 0.45, size: 1.5, brightness: 0.6, pulseSpeed: 0.005, pulsePhase: 3 }
            ],
            connections: [[0, 1], [1, 2], [2, 3], [3, 0]]
        },
        // SCORPIO (Scorpion) - Ligne 2, Position 4
        {
            name: 'SCORPIO',
            position: { x: 0.875, y: 0.4 },
            label: { x: 0.875, y: 0.33, text: 'S C O R P I O' },
            stars: [
                { x: 0.85, y: 0.38, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.88, y: 0.42, size: 2.0, brightness: 0.9, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.90, y: 0.48, size: 1.8, brightness: 0.8, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.92, y: 0.45, size: 1.6, brightness: 0.7, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.87, y: 0.35, size: 1.4, brightness: 0.6, pulseSpeed: 0.003, pulsePhase: 4 }
            ],
            connections: [[0, 1], [1, 2], [2, 3], [0, 4]]
        },
        // SAGITTARIUS (Sagittaire) - Ligne 3, Position 1
        {
            name: 'SAGITTARIUS',
            position: { x: 0.125, y: 0.65 },
            label: { x: 0.125, y: 0.58, text: 'S A G I T T A R I U S' },
            stars: [
                { x: 0.08, y: 0.62, size: 2.0, brightness: 0.9, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.12, y: 0.65, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.15, y: 0.68, size: 2.2, brightness: 1.0, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.18, y: 0.65, size: 1.6, brightness: 0.7, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.10, y: 0.70, size: 1.4, brightness: 0.6, pulseSpeed: 0.003, pulsePhase: 4 }
            ],
            connections: [[0, 1], [1, 2], [2, 3], [1, 4]]
        },
        // CAPRICORN (Capricorne) - Ligne 3, Position 2
        {
            name: 'CAPRICORN',
            position: { x: 0.375, y: 0.65 },
            label: { x: 0.375, y: 0.58, text: 'C A P R I C O R N' },
            stars: [
                { x: 0.35, y: 0.62, size: 2.5, brightness: 1.0, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.38, y: 0.68, size: 2.0, brightness: 0.9, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.42, y: 0.65, size: 1.8, brightness: 0.8, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.40, y: 0.72, size: 1.6, brightness: 0.7, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.36, y: 0.75, size: 1.4, brightness: 0.6, pulseSpeed: 0.003, pulsePhase: 4 }
            ],
            connections: [[0, 1], [1, 2], [1, 3], [3, 4]]
        },
        // AQUARIUS (Verseau) - Ligne 3, Position 3
        {
            name: 'AQUARIUS',
            position: { x: 0.625, y: 0.65 },
            label: { x: 0.625, y: 0.58, text: 'A Q U A R I U S' },
            stars: [
                { x: 0.60, y: 0.62, size: 2.0, brightness: 0.9, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.63, y: 0.68, size: 2.2, brightness: 1.0, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.67, y: 0.65, size: 1.8, brightness: 0.8, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.65, y: 0.72, size: 1.6, brightness: 0.7, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.58, y: 0.70, size: 1.4, brightness: 0.6, pulseSpeed: 0.003, pulsePhase: 4 }
            ],
            connections: [[0, 1], [1, 2], [1, 3], [0, 4]]
        },
        // PISCES (Poissons) - Ligne 3, Position 4
        {
            name: 'PISCES',
            position: { x: 0.875, y: 0.65 },
            label: { x: 0.875, y: 0.58, text: 'P I S C E S' },
            stars: [
                { x: 0.85, y: 0.62, size: 2.0, brightness: 0.9, pulseSpeed: 0.003, pulsePhase: 0 },
                { x: 0.88, y: 0.68, size: 1.8, brightness: 0.8, pulseSpeed: 0.004, pulsePhase: 1 },
                { x: 0.90, y: 0.65, size: 2.2, brightness: 1.0, pulseSpeed: 0.002, pulsePhase: 2 },
                { x: 0.92, y: 0.72, size: 1.6, brightness: 0.7, pulseSpeed: 0.005, pulsePhase: 3 },
                { x: 0.87, y: 0.75, size: 1.4, brightness: 0.6, pulseSpeed: 0.003, pulsePhase: 4 },
                { x: 0.83, y: 0.70, size: 1.3, brightness: 0.5, pulseSpeed: 0.004, pulsePhase: 5 }
            ],
            connections: [[0, 1], [1, 2], [2, 3], [3, 4], [4, 5], [5, 0]]
        }
    ], [zodiacConstellations]);

    // Couleurs des étoiles
    const starColors = useMemo(() => [
        '#FFFFFF', '#F0F8FF', '#87CEEB', '#B0E0E6', '#E0FFFF'
    ], []);

    // Étoiles de fond pour l'ambiance
    const backgroundStars = useMemo(() => {
        const stars: BackgroundStar[] = [];
        for (let i = 0; i < 150; i++) {
            stars.push({
                x: Math.random(),
                y: Math.random(),
                size: Math.random() * 0.8 + 0.2,
                opacity: Math.random() * 0.3 + 0.1,
                pulseSpeed: Math.random() * 0.002 + 0.001,
                pulsePhase: Math.random() * Math.PI * 2
            });
        }
        return stars;
    }, []);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d', {
            alpha: false,
            desynchronized: true
        });
        if (!ctx) return;

        let mouseX = -1;
        let mouseY = -1;

        const initialize = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };

        // Fonction pour dessiner une étoile de constellation
        const drawConstellationStar = (star: ConstellationStar, canvasX: number, canvasY: number, time: number) => {
            const twinkle = Math.sin(time * star.pulseSpeed + star.pulsePhase) * 0.2 + 0.8;
            const currentOpacity = star.brightness * twinkle;
            const color = starColors[0]; // Blanc pour toutes les étoiles

            ctx.save();
            ctx.globalAlpha = currentOpacity;

            // Halo pour les étoiles brillantes
            if (star.brightness > 0.7) {
                const gradient = ctx.createRadialGradient(
                    canvasX, canvasY, 0,
                    canvasX, canvasY, star.size * 3
                );
                gradient.addColorStop(0, color);
                gradient.addColorStop(0.3, color + '80');
                gradient.addColorStop(1, color + '00');

                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(canvasX, canvasY, star.size * 3, 0, Math.PI * 2);
                ctx.fill();
            }

            // Corps de l'étoile
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(canvasX, canvasY, star.size, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();
        };

        // Fonction pour dessiner les connexions d'une constellation
        const drawConstellationConnections = (constellation: Constellation, canvasWidth: number, canvasHeight: number) => {
            ctx.save();
            ctx.globalAlpha = 0.4;
            ctx.strokeStyle = starColors[0];
            ctx.lineWidth = 1;

            constellation.connections.forEach(([startIdx, endIdx]) => {
                const startStar = constellation.stars[startIdx];
                const endStar = constellation.stars[endIdx];

                if (startStar && endStar) {
                    const startX = startStar.x * canvasWidth;
                    const startY = startStar.y * canvasHeight;
                    const endX = endStar.x * canvasWidth;
                    const endY = endStar.y * canvasHeight;

                    ctx.beginPath();
                    ctx.moveTo(startX, startY);
                    ctx.lineTo(endX, endY);
                    ctx.stroke();
                }
            });

            ctx.restore();
        };

        // Fonction pour dessiner le label d'une constellation
        const drawConstellationLabel = (constellation: Constellation, canvasWidth: number, canvasHeight: number) => {
            const labelX = constellation.label.x * canvasWidth;
            const labelY = constellation.label.y * canvasHeight;

            ctx.save();
            ctx.globalAlpha = 0.6;
            ctx.fillStyle = starColors[0];
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(constellation.label.text, labelX, labelY);
            ctx.restore();
        };

        // Fonction pour dessiner les étoiles de fond
        const drawBackgroundStars = (time: number) => {
            backgroundStars.forEach(star => {
                const twinkle = Math.sin(time * star.pulseSpeed + star.pulsePhase) * 0.3 + 0.7;
                const currentOpacity = star.opacity * twinkle;

                ctx.save();
                ctx.globalAlpha = currentOpacity;
                ctx.fillStyle = starColors[0];
                ctx.beginPath();
                ctx.arc(star.x * canvas.width, star.y * canvas.height, star.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            });
        };

        // Animation des constellations du zodiaque
        const animate = (time: number) => {
            const deltaTime = time - lastFrameTimeRef.current;
            const targetFrameTime = 1000 / 60; // 60 FPS

            if (deltaTime < targetFrameTime) {
                animationIdRef.current = requestAnimationFrame(animate);
                return;
            }

            lastFrameTimeRef.current = time;

            // Fond noir étoilé
            ctx.fillStyle = '#000011';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Dessiner les étoiles de fond
            drawBackgroundStars(time);

            // Dessiner chaque constellation du zodiaque
            allConstellations.forEach(constellation => {
                // Dessiner les connexions en premier
                drawConstellationConnections(constellation, canvas.width, canvas.height);

                // Dessiner les étoiles
                constellation.stars.forEach(star => {
                    const canvasX = star.x * canvas.width;
                    const canvasY = star.y * canvas.height;
                    drawConstellationStar(star, canvasX, canvasY, time);
                });

                // Dessiner le label
                drawConstellationLabel(constellation, canvas.width, canvas.height);
            });

            // Effet de connexion avec la souris (optionnel)
            if (mouseX >= 0 && mouseY >= 0) {
                // Trouver les étoiles proches de la souris
                allConstellations.forEach(constellation => {
                    constellation.stars.forEach(star => {
                        const canvasX = star.x * canvas.width;
                        const canvasY = star.y * canvas.height;
                        const dx = mouseX - canvasX;
                        const dy = mouseY - canvasY;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < 100) {
                            const opacity = Math.max(0, (100 - distance) / 100) * 0.3;
                            ctx.save();
                            ctx.globalAlpha = opacity;
                            ctx.strokeStyle = starColors[0];
                            ctx.lineWidth = 1;
                            ctx.beginPath();
                            ctx.moveTo(canvasX, canvasY);
                            ctx.lineTo(mouseX, mouseY);
                            ctx.stroke();
                            ctx.restore();
                        }
                    });
                });

                // Point souris
                ctx.save();
                ctx.globalAlpha = 0.5;
                ctx.fillStyle = starColors[0];
                ctx.beginPath();
                ctx.arc(mouseX, mouseY, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }

            animationIdRef.current = requestAnimationFrame(animate);
        };

        // Gestionnaires d'événements
        const handleResize = () => {
            initialize();
        };

        const handleMouseMove = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        const handleMouseLeave = () => {
            mouseX = -1;
            mouseY = -1;
        };

        const handleMouseEnter = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            mouseX = event.clientX - rect.left;
            mouseY = event.clientY - rect.top;
        };

        // Initialisation
        initialize();
        animate(0);

        // Event listeners
        window.addEventListener('resize', handleResize);
        canvas.addEventListener('mousemove', handleMouseMove);
        canvas.addEventListener('mouseleave', handleMouseLeave);
        canvas.addEventListener('mouseenter', handleMouseEnter);

        return () => {
            if (animationIdRef.current) {
                cancelAnimationFrame(animationIdRef.current);
            }
            window.removeEventListener('resize', handleResize);
            canvas.removeEventListener('mousemove', handleMouseMove);
            canvas.removeEventListener('mouseleave', handleMouseLeave);
            canvas.removeEventListener('mouseenter', handleMouseEnter);
        };
    }, []);

    return (
        <div className="fixed top-0 left-0 w-full h-full -z-10" style={{ background: '#000' }}>
            <canvas
                ref={canvasRef}
                className="absolute top-0 left-0 w-full h-full"
                style={{
                    pointerEvents: 'auto',
                    zIndex: 1
                }}
            />
        </div>
    );
};

export default ConstellationBackground;
